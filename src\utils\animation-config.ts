/**
 * Performance-optimized animation configurations
 * Reduces animation overhead and improves performance
 */

import { Variants, Transition } from 'framer-motion'

// Check if user prefers reduced motion
export const prefersReducedMotion = () => {
  if (typeof window === 'undefined') return false
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches
}

// Optimized transition configurations
export const transitions = {
  // Fast transitions for better performance
  fast: {
    duration: 0.2,
    ease: [0.4, 0, 0.2, 1] as const
  } as Transition,
  
  // Standard transitions
  standard: {
    duration: 0.3,
    ease: [0.4, 0, 0.2, 1] as const
  } as Transition,
  
  // Smooth transitions for important elements
  smooth: {
    duration: 0.4,
    ease: [0.4, 0, 0.2, 1] as const
  } as Transition,
  
  // Spring transitions (use sparingly)
  spring: {
    type: 'spring' as const,
    stiffness: 300,
    damping: 30
  } as Transition
}

// Optimized animation variants
export const fadeInUp: Variants = {
  hidden: { 
    opacity: 0, 
    y: 20,
    transition: transitions.fast
  },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: transitions.standard
  }
}

export const fadeIn: Variants = {
  hidden: { 
    opacity: 0,
    transition: transitions.fast
  },
  visible: { 
    opacity: 1,
    transition: transitions.standard
  }
}

export const scaleIn: Variants = {
  hidden: { 
    opacity: 0, 
    scale: 0.95,
    transition: transitions.fast
  },
  visible: { 
    opacity: 1, 
    scale: 1,
    transition: transitions.standard
  }
}

export const slideInLeft: Variants = {
  hidden: { 
    opacity: 0, 
    x: -30,
    transition: transitions.fast
  },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: transitions.standard
  }
}

export const slideInRight: Variants = {
  hidden: { 
    opacity: 0, 
    x: 30,
    transition: transitions.fast
  },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: transitions.standard
  }
}

// Container variants for staggered animations
export const staggerContainer: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
}

export const staggerItem: Variants = {
  hidden: { 
    opacity: 0, 
    y: 20 
  },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: transitions.standard
  }
}

// Hover animations (reduced for performance)
export const hoverScale = {
  scale: 1.02,
  transition: transitions.fast
}

export const hoverLift = {
  y: -4,
  transition: transitions.fast
}

// Viewport configuration for better performance
export const viewportConfig = {
  once: true,
  margin: "-10% 0px -10% 0px",
  amount: 0.3
}

// Performance-optimized motion props
export const motionProps = {
  // Reduce layout calculations
  layout: false,
  // Optimize for transform animations
  style: {
    willChange: 'transform, opacity'
  }
}

// Conditional animation based on reduced motion preference
export const getAnimation = (animation: Variants) => {
  return prefersReducedMotion() ? fadeIn : animation
}

// Conditional transition based on reduced motion preference
export const getTransition = (transition: Transition) => {
  return prefersReducedMotion() ? transitions.fast : transition
}

// Performance monitoring for animations
export const logAnimationPerformance = (componentName: string, startTime: number) => {
  if (process.env.NODE_ENV === 'development') {
    const endTime = performance.now()
    const duration = endTime - startTime
    
    if (duration > 16) { // 60fps threshold
      console.warn(`⚠️ Animation Performance: ${componentName} took ${duration.toFixed(2)}ms`)
    }
  }
}
